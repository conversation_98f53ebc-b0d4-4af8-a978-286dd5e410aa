# Enhanced DragDropPanel Widget

This document describes the enhanced drag-and-drop widget implementation for the `DragDropPanel` class.

## Features Implemented

### 1. Visual Design
- **Dashed borders**: Added dashed borders at the top and bottom of the widget to clearly indicate the drop zone
- **Plus icon**: Centered image icon with a "plus" sign (SVG format with fallback to Unicode character)
- **Updated text**: Changed from Russian placeholder text to "Drag / Paste / Click to upload an image"
- **Clean layout**: Centered layout with proper spacing between icon and text

### 2. Interactive Elements
- **Hover cursor**: Changes from default arrow to hand pointer when hovering over the widget
- **Hover border color**: Dashed border changes to purple (#6f42c1) on hover to indicate clickable area
- **Drag feedback**: Visual feedback during drag operations with green border and background color change

### 3. Implementation Details

#### Files Created/Modified:
1. **`ui/main_window.py`**: Enhanced DragDropPanel class and added PlaceholderWidget class
2. **`ui/styles/dragdrop_styles.qss`**: QSS stylesheet for visual styling
3. **`ui/resources/plus_icon.svg`**: SVG icon for the upload indicator

#### Key Classes:
- **`PlaceholderWidget`**: Custom widget that displays the icon and text
- **`DragDropPanel`**: Enhanced with styling, hover effects, and visual feedback

### 4. Maintained Functionality
- **File selection via clicking**: Original click-to-browse functionality preserved
- **Drag-and-drop acceptance**: All original drag-and-drop behavior maintained
- **Outliner display**: File processing and outliner display unchanged

## Usage

The enhanced widget is automatically used when creating a `DragDropPanel` instance:

```python
from ui.main_window import DragDropPanel

# Create the enhanced drag-drop panel
panel = DragDropPanel()
```

## Styling

The widget uses QSS (Qt Style Sheets) for styling. The styles are automatically loaded from:
- `ui/styles/dragdrop_styles.qss`

### Key Style Features:
- Dashed border with hover effects
- Purple accent color (#6f42c1) for interactive states
- Green feedback color (#28a745) during drag operations
- Responsive cursor changes

## Testing

Test scripts are provided:
- `test_placeholder.py`: Tests the PlaceholderWidget in isolation
- `test_dragdrop.py`: Tests the complete DragDropPanel functionality

## File Structure

```
ui/
├── main_window.py          # Enhanced DragDropPanel and PlaceholderWidget
├── styles/
│   └── dragdrop_styles.qss # QSS stylesheet
└── resources/
    └── plus_icon.svg       # Upload icon (SVG format)
```

## Fallback Behavior

The implementation includes robust fallback mechanisms:
- If the SVG icon cannot be loaded, falls back to Unicode plus symbol (⊕)
- If styles cannot be loaded, the widget still functions with default Qt styling
- All original functionality is preserved even if enhancements fail to load

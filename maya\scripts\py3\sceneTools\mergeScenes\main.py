import importlib
import sceneTools.mergeScenes.ui.main_window as MainWindow

# importlib.reload(AtlasPackWnd)
import maya.cmds as cmds
import sys


# def show_main_window():
#     """Instantiate and show the modal merge scenes window."""
#     global _merge_scenes_window
#     try:
#         _merge_scenes_window.close()
#     except Exception:
#         pass
#     _merge_scenes_window = MergeScenesWindow()
#     _merge_scenes_window.show()


def show_main_window():
	if cmds.window("MergeScenesMainWindow", exists=True):
		cmds.deleteUI("MergeScenesMainWindow")
	try:
		main_window = MainWindow.MergeScenesWindow()
		# main_window.setParent(MergeScenesWnd.get_maya_main_window())
		main_window.show()
	# main_window.set_size_preview()
	except RuntimeError as e:
		print(e)


def reload_all_modules(module_name):
	print('loaded {}'.format(module_name))
	for m in list(sys.modules):
		if module_name in m:
			print('deleted {}'.format(m))
			del (sys.modules[m])


def main():
	"""Main function to run the Atlas Pack tool."""
	reload_all_modules('mergeScenes')
	show_main_window()


# Example usage
if __name__ == "__main__":
	reload_all_modules('mergeScenes')
	show_main_window()

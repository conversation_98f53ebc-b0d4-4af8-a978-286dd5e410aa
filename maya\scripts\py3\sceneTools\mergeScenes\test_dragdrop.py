#!/usr/bin/env python
"""
Test script for the enhanced DragDropPanel widget.
This script can be run to test the drag-drop functionality without <PERSON>.
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtCore import Qt

# Add the current directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import our custom widgets
from ui.main_window import DragDropPanel, PlaceholderWidget


class TestWindow(QMainWindow):
    """Test window to showcase the DragDropPanel."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("DragDropPanel Test")
        self.setGeometry(100, 100, 600, 400)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QVBoxLayout(central_widget)
        
        # Create and add the DragDropPanel
        self.drag_drop_panel = DragDropPanel()
        layout.addWidget(self.drag_drop_panel)


def main():
    """Main function to run the test."""
    app = QApplication(sys.argv)
    
    # Create and show the test window
    window = TestWindow()
    window.show()
    
    # Run the application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()

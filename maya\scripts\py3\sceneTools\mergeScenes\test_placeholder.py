#!/usr/bin/env python
"""
Simple test for the PlaceholderWidget to verify it works correctly.
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtCore import Qt

# Add the current directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import our custom widgets
from ui.main_window import PlaceholderWidget


class TestPlaceholderWindow(QMainWindow):
    """Test window to showcase the PlaceholderWidget."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PlaceholderWidget Test")
        self.setGeometry(100, 100, 400, 300)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QVBoxLayout(central_widget)
        
        # Create and add the PlaceholderWidget
        self.placeholder_widget = PlaceholderWidget()
        layout.addWidget(self.placeholder_widget)


def main():
    """Main function to run the test."""
    app = QApplication(sys.argv)
    
    # Create and show the test window
    window = TestPlaceholderWindow()
    window.show()
    
    print("PlaceholderWidget test window created successfully!")
    print("You should see a widget with a plus icon and text.")
    
    # Run the application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()

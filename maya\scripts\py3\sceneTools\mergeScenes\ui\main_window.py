import os
from PySide6.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, Q<PERSON>plitter, QVBoxLayout, QWidget, QHBoxLayout, QLabel, QFileDialog
from PySide6.QtCore import Qt
from PySide6.QtGui import QDragEnterEvent, QDropEvent, QMouseEvent, QPixmap, QIcon
import maya.OpenMayaUI as omui
from shiboken6 import wrapInstance
import maya.cmds as cmds


def maya_main_window():
	"""Return Maya's main window widget as a Python object"""
	ptr = omui.MQtUtil.mainWindow()
	return wrapInstance(int(ptr), QWidget)


class PlaceholderWidget(QWidget):
	"""Custom placeholder widget with icon and text for drag-drop area."""

	def __init__(self, parent=None):
		super(PlaceholderWidget, self).__init__(parent)
		self.setObjectName("placeholder-widget")
		self._setup_ui()

	def _setup_ui(self):
		"""Set up the placeholder UI with icon and text."""
		layout = QVBoxLayout(self)
		layout.setAlignment(Qt.AlignCenter)
		layout.setSpacing(15)

		# Create icon label
		icon_label = QLabel()
		icon_label.setObjectName("upload-icon")
		icon_label.setAlignment(Qt.AlignCenter)

		# Try to load the SVG icon, fallback to text if not available
		icon_path = os.path.join(os.path.dirname(__file__), "resources", "plus_icon.svg")
		if os.path.exists(icon_path):
			pixmap = QPixmap(icon_path)
			if not pixmap.isNull():
				# Scale the icon to a reasonable size
				scaled_pixmap = pixmap.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
				icon_label.setPixmap(scaled_pixmap)
			else:
				# Fallback to text icon
				icon_label.setText("⊕")
				icon_label.setStyleSheet("font-size: 48px; color: #6c757d;")
		else:
			# Fallback to text icon
			icon_label.setText("⊕")
			icon_label.setStyleSheet("font-size: 48px; color: #6c757d;")

		# Create text label
		text_label = QLabel("Drag / Paste / Click to upload an image")
		text_label.setObjectName("upload-text")
		text_label.setAlignment(Qt.AlignCenter)

		# Add widgets to layout
		layout.addWidget(icon_label)
		layout.addWidget(text_label)


class OutlinerPanel(QWidget):
	"""A widget embedding a Maya Outliner panel."""

	def __init__(self, parent=None, reference_file=None):
		super(OutlinerPanel, self).__init__(parent)
		self.reference_file = reference_file
		self._create_outliner()

	def _create_outliner(self):
		# Reference file if provided
		if self.reference_file:
			try:
				# Unload previous references in this namespace
				cmds.file(self.reference_file, reference=True, namespace='dropped_scene')
			except Exception:
				pass
		# Create a pane and outliner panel
		pane = cmds.paneLayout()
		panel = cmds.outlinerPanel()
		# Query the outliner editor
		editor = cmds.outlinerPanel(panel, query=True, outlinerEditor=True)
		# Configure the editor
		if self.reference_file:
			cmds.outlinerEditor(editor, edit=True,
			                    showReferenceNodes=True,
			                    showDagOnly=True,
			                    mainListConnection='worldList')
		else:
			cmds.outlinerEditor(editor, edit=True,
			                    mainListConnection='worldList')
		# Embed into Qt
		ptr = omui.MQtUtil.findControl(panel)
		if ptr:
			widget = wrapInstance(int(ptr), QWidget)
			lay = QHBoxLayout(self)
			lay.setContentsMargins(0, 0, 0, 0)
			lay.addWidget(widget)


class DragDropPanel(QWidget):
	"""Left panel: accepts drag-and-drop of Maya scene file and shows its outliner."""

	def __init__(self, parent=None):
		super(DragDropPanel, self).__init__(parent)
		self.setAcceptDrops(True)
		self.setObjectName("DragDropPanel")
		self.layout = QVBoxLayout(self)
		self.layout.setContentsMargins(10, 10, 10, 10)
		self._load_styles()
		self._show_placeholder()
		self.outliner_widget = None
		self.placeholder_widget = None

	def _load_styles(self):
		"""Load and apply QSS styles to the widget."""
		try:
			style_path = os.path.join(os.path.dirname(__file__), "styles", "dragdrop_styles.qss")
			if os.path.exists(style_path):
				with open(style_path, 'r') as f:
					style_sheet = f.read()
				self.setStyleSheet(style_sheet)
		except Exception as e:
			print(f"Could not load styles: {e}")

	def _show_placeholder(self):
		"""Show the custom placeholder widget with icon and text."""
		self.placeholder_widget = PlaceholderWidget(self)
		self.layout.addWidget(self.placeholder_widget)

	def dragEnterEvent(self, event: QDragEnterEvent):
		if event.mimeData().hasUrls():
			# Set visual feedback for drag over
			self.setProperty("dragover", True)
			self.style().polish(self)
			event.acceptProposedAction()

	def dragLeaveEvent(self, event):
		# Remove visual feedback when drag leaves
		self.setProperty("dragover", False)
		self.style().polish(self)
		super().dragLeaveEvent(event)

	def dropEvent(self, event: QDropEvent):
		# Remove visual feedback
		self.setProperty("dragover", False)
		self.style().polish(self)

		urls = event.mimeData().urls()
		if urls:
			file_path = urls[0].toLocalFile()
			if os.path.isfile(file_path):
				self._display_outliner(file_path)
		event.acceptProposedAction()

	def enterEvent(self, event):
		"""Handle mouse enter for hover effects."""
		self.setCursor(Qt.PointingHandCursor)
		super().enterEvent(event)

	def leaveEvent(self, event):
		"""Handle mouse leave to restore cursor."""
		self.setCursor(Qt.ArrowCursor)
		super().leaveEvent(event)

	def _display_outliner(self, file_path):
		# Clear existing placeholder or widget
		while self.layout.count():
			item = self.layout.takeAt(0)
			widget = item.widget()
			if widget:
				widget.setParent(None)
		# Add outliner of the dropped scene
		self.outliner_widget = OutlinerPanel(self, reference_file=file_path)
		self.layout.addWidget(self.outliner_widget)

	def mousePressEvent(self, event: QMouseEvent):
		if event.button() == Qt.LeftButton:
			file_path, _ = QFileDialog.getOpenFileName(
				self, "Выберите файл сцены", "", "Maya Scene Files (*.ma *.mb)"
			)
			if file_path:
				self._display_outliner(file_path)
		super().mousePressEvent(event)


class MergeScenesWindow(QDialog):
	"""Main window: modal dialog with two panels side by side."""

	def __init__(self, parent=None):
		super(MergeScenesWindow, self).__init__(parent or maya_main_window())
		self.setObjectName("MergeScenesMainWindow")
		self.setWindowTitle("Merge Scenes")
		self.setModal(True)
		self.resize(800, 600)
		self._build_ui()

	def _build_ui(self):
		splitter = QSplitter(Qt.Horizontal, self)
		# Left drag-drop panel
		left = DragDropPanel()
		# Right panel immediately showing current scene outliner
		right = OutlinerPanel()
		splitter.addWidget(left)
		splitter.addWidget(right)
		main_layout = QVBoxLayout(self)
		main_layout.addWidget(splitter)

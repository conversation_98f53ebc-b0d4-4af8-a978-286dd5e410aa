import os
from PySide6.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, QVBoxLayout, QWidget, QHBoxLayout, QLabel, QFileDialog
from PySide6.QtCore import Qt
from PySide6.QtGui import QDragEnterEvent, QDropEvent, QMouseEvent
import maya.OpenMayaUI as omui
from shiboken6 import wrapInstance
import maya.cmds as cmds


def maya_main_window():
	"""Return Maya's main window widget as a Python object"""
	ptr = omui.MQtUtil.mainWindow()
	return wrapInstance(int(ptr), QWidget)


class OutlinerPanel(QWidget):
	"""A widget embedding a Maya Outliner panel."""

	def __init__(self, parent=None, reference_file=None):
		super(OutlinerPanel, self).__init__(parent)
		self.reference_file = reference_file
		self._create_outliner()

	def _create_outliner(self):
		# Reference file if provided
		if self.reference_file:
			try:
				# Unload previous references in this namespace
				cmds.file(self.reference_file, reference=True, namespace='dropped_scene')
			except Exception:
				pass
		# Create a pane and outliner panel
		pane = cmds.paneLayout()
		panel = cmds.outlinerPanel()
		# Query the outliner editor
		editor = cmds.outlinerPanel(panel, query=True, outlinerEditor=True)
		# Configure the editor
		if self.reference_file:
			cmds.outlinerEditor(editor, edit=True,
			                    showReferenceNodes=True,
			                    showDagOnly=True,
			                    mainListConnection='worldList')
		else:
			cmds.outlinerEditor(editor, edit=True,
			                    mainListConnection='worldList')
		# Embed into Qt
		ptr = omui.MQtUtil.findControl(panel)
		if ptr:
			widget = wrapInstance(int(ptr), QWidget)
			lay = QHBoxLayout(self)
			lay.setContentsMargins(0, 0, 0, 0)
			lay.addWidget(widget)


class DragDropPanel(QWidget):
	"""Left panel: accepts drag-and-drop of Maya scene file and shows its outliner."""

	def __init__(self, parent=None):
		super(DragDropPanel, self).__init__(parent)
		self.setAcceptDrops(True)
		self.layout = QVBoxLayout(self)
		self._show_placeholder()
		self.outliner_widget = None

	def _show_placeholder(self):
		# Placeholder label before drop
		label = QLabel("Перетащите файл сцены из файлового менеджера")
		label.setAlignment(Qt.AlignCenter)
		self.layout.addWidget(label)

	def dragEnterEvent(self, event: QDragEnterEvent):
		if event.mimeData().hasUrls():
			event.acceptProposedAction()

	def dropEvent(self, event: QDropEvent):
		urls = event.mimeData().urls()
		if urls:
			file_path = urls[0].toLocalFile()
			if os.path.isfile(file_path):
				self._display_outliner(file_path)
		event.acceptProposedAction()

	def _display_outliner(self, file_path):
		# Clear existing placeholder or widget
		while self.layout.count():
			item = self.layout.takeAt(0)
			widget = item.widget()
			if widget:
				widget.setParent(None)
		# Add outliner of the dropped scene
		self.outliner_widget = OutlinerPanel(self, reference_file=file_path)
		self.layout.addWidget(self.outliner_widget)

	def mousePressEvent(self, event: QMouseEvent):
		if event.button() == Qt.LeftButton:
			file_path, _ = QFileDialog.getOpenFileName(
				self, "Выберите файл сцены", "", "Maya Scene Files (*.ma *.mb)"
			)
			if file_path:
				self._display_outliner(file_path)
		super().mousePressEvent(event)


class MergeScenesWindow(QDialog):
	"""Main window: modal dialog with two panels side by side."""

	def __init__(self, parent=None):
		super(MergeScenesWindow, self).__init__(parent or maya_main_window())
		self.setObjectName("MergeScenesMainWindow")
		self.setWindowTitle("Merge Scenes")
		self.setModal(True)
		self.resize(800, 600)
		self._build_ui()

	def _build_ui(self):
		splitter = QSplitter(Qt.Horizontal, self)
		# Left drag-drop panel
		left = DragDropPanel()
		# Right panel immediately showing current scene outliner
		right = OutlinerPanel()
		splitter.addWidget(left)
		splitter.addWidget(right)
		main_layout = QVBoxLayout(self)
		main_layout.addWidget(splitter)

/* Drag and Drop Panel Styles */

/* Main drag-drop panel */
DragDropPanel {
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    min-height: 200px;
}

/* Hover state for drag-drop panel */
DragDropPanel:hover {
    border-color: #6f42c1;
}

/* Placeholder container widget */
QWidget#placeholder-widget {
    background-color: transparent;
    border: none;
}

/* Upload icon label */
QLabel#upload-icon {
    background-color: transparent;
    border: none;
    padding: 10px;
}

/* Upload text label */
QLabel#upload-text {
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
    background-color: transparent;
    border: none;
    padding: 5px;
}

/* Hover state for upload text */
QLabel#upload-text:hover {
    color: #6f42c1;
}

/* Active/focus states */
DragDropPanel:focus {
    border-color: #6f42c1;
    outline: none;
}

/* When dragging over */
DragDropPanel[dragover="true"] {
    border-color: #28a745;
    background-color: #f8fff9;
}

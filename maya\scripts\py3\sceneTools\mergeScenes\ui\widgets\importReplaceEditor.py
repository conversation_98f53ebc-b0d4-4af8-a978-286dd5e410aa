from PySide2.QtGui import *
from PySide2.QtCore import *
from PySide2.QtWidgets import *
import shiboken2
from shiboken2 import wrapInstance
import maya.OpenMayaUI as omu
import maya.cmds as cmds
import os, posixpath


def main_window_pointer():
    ptr = omu.MQtUtil.mainWindow()  # pointer for the main window
    return wrapInstance(int(ptr), QWidget)


class Utils(object):
    @classmethod
    def maya_scene_name(cls):
        raw_file_path = cmds.file(q=True, exn=True)
        if raw_file_path:
            path_name = posixpath.split(raw_file_path)
            return path_name[0], path_name[1]
        return '', ''

    @classmethod
    def delete_reference(cls):
        for ref in cmds.ls(references=True):
            try:
                cmds.file(unloadReference=ref)
            except:
                pass
        for ref in cmds.ls(references=True):
            try:
                cmds.file(rr=1, f=1, referenceNode=ref)
            except:
                pass

        cls.without_material()

    @classmethod
    def without_material(cls):
        empty_shading = []
        meshes = cmds.ls(o=1, s=1)
        meshes = cmds.filterExpand(meshes, sm=12)
        if meshes:
            meshes = cmds.listRelatives(meshes, s=True, f=1)
            for m in meshes:
                shader = cmds.listConnections(m, type='shadingEngine')
                if not shader:
                    empty_shading.append(m)

        empty_shading = cmds.listRelatives(empty_shading, p=True, f=1)
        cls.objects_without_materials_util(empty_shading)
        cmds.select(d=1)

    @classmethod
    def objects_without_materials_util(cls, meshes):
        if meshes:
            for i in meshes:
                if "gun" in i or "Gun" in i:
                    cls.correct_mat(i, "tank_guns")
                elif "turret" in i or "Turret" in i:
                    cls.correct_mat(i, "tank_turret_01")
                elif "hull" in i or "Hull" in i:
                    cls.correct_mat(i, "tank_hull_01")
                elif "track_L" in i or "Track_L" in i:
                    cls.correct_mat(i, "track_mat_L")
                elif "track_R" in i or "Track_R" in i:
                    cls.correct_mat(i, "track_mat_R")
                elif "chassis" in i or "Chassis" in i or "w_" in i or "W_" in i or "wd_" in i or "Wd_" in i:
                    cls.correct_mat(i, "tank_chassis_01")
        return []

    @classmethod
    def correct_mat(cls, obj, matName):
        existingMatList = cmds.ls(mat=1)
        correctMatName = matName

        if obj.find("turret") != -1 or obj.find("Turret") != -1:
            num = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"]
            if obj[-1] in num:
                correctMatName = correctMatName.replace(correctMatName[-1], obj[-1])

        if correctMatName in existingMatList:
            # if material exists
            cmds.select(obj)
            cmds.hyperShade(assign=correctMatName)
        else:
            # create new material and assign it
            newMat = cmds.shadingNode('lambert', n=correctMatName, asShader=1)
            cmds.select(obj)
            cmds.hyperShade(assign=newMat)

    @classmethod
    def visible_reference_objects(cls):
        meshes = cmds.ls('*:*', o=1, s=1)
        for m in meshes:
            cmds.setAttr(m + '.visibility', 0)

    @classmethod
    def reference_load_unload(cls, file):
        path, file_short = cls.maya_scene_name()
        cls.delete_reference()
        if file_short in file:
            cmds.confirmDialog(title='Warning', message='Current file opened', button=['   OK   '],
                               defaultButton='   OK   ')
        else:
            cmds.file(file, reference=True, namespace='r')
            filters = cmds.itemFilter(bn=('*:*'), neg=1)
            cmds.outlinerEditor('outlinerPanel1', e=1, showReferenceNodes=False, showReferenceMembers=False,
                                filter=filters)
        cls.visible_reference_objects()

    @classmethod
    def import_action(cls):
        sel = cmds.ls(sl=1)
        if sel:
            new = cmds.duplicate(sel)
            new = cmds.ls(new, l=1)
            meshes = cmds.listRelatives(new, ad=1, s=1, f=1)
            for m in meshes:
                cmds.setAttr(m + '.visibility', 1)
            try:
                cmds.parent(w=1)
            except:
                pass
        else:
            cmds.confirmDialog(title='Warning', message='Select object(s) from left panel', button=['   OK   '],
                               defaultButton='   OK   ')

    @classmethod
    def replace_action(cls):
        pair = cmds.filterExpand(fp=1, sm=12)
        if len(pair) == 2:
            if 'r:' in pair[0] and 'r:' not in pair[1]:
                cmds.connectAttr(pair[0] + '.outMesh', pair[1] + '.inMesh',
                                 f=1)  # -f r:gun_02_Shape.outMesh gun_02_Shape.inMesh;
        else:
            cmds.confirmDialog(title='Warning', message='Select one object from left panel and one from right panel',
                               button=['   OK   '], defaultButton='   OK   ')

    @classmethod
    def two_up_folder(cls, file):
        two_up = os.path.abspath(os.path.join(file, "../.."))
        return two_up

    @classmethod
    def source_folder(cls, two_up_folder, folder_name):
        srs_folder = None
        for root, dirs, files in os.walk(two_up_folder):
            for name in dirs:
                if name == folder_name:
                    print((os.path.join(root, name)))
                    srs_folder = os.path.join(root, name)
                    break
        if srs_folder:
            return srs_folder
        else:
            cmds.confirmDialog(title='Warning', message='Directory ' + folder_name + ' doesn`t exist',
                               button=['   OK   '], defaultButton='   OK   ')
            cmds.error()

    @classmethod
    def source_file(cls, source_folder, file_name):
        srs_file = None
        for root, dirs, files in os.walk(source_folder):
            for name in files:
                if name == file_name:
                    print((os.path.join(root, name)))
                    srs_file = os.path.join(root, name)
                    break
        if srs_file:
            return srs_file
        else:
            cmds.confirmDialog(title='Warning', message='File ' + source_folder + '/' + file_name + ' doesn`t exist',
                               button=['   OK   '], defaultButton='   OK   ')
            cmds.error()

    @classmethod
    def silent_mode_load(cls, source_file):
        cls.delete_reference()
        cmds.file(source_file, reference=True, namespace='r')

    @classmethod
    def silent_mode_selection(cls):
        # selected = cmds.listRelatives(cmds.filterExpand(fp=1, sm=12), p=1, pa=1)
        selected = cmds.ls(sl=1, l=1, tr=1)
        if selected:
            return selected
        else:
            cmds.confirmDialog(title='Warning', message='Select some mesh(es)', button=['   OK   '],
                               defaultButton='   OK   ')
            cmds.error()

    @classmethod
    def silent_mode_references(cls, item):
        reference = item.replace("|", "|r:")

        if not cmds.objExists(reference):
            cmds.confirmDialog(title='Warning', message='Object ' + item + ' doesn`t exist in source file', \
                               button=[' OK '], defaultButton='   OK   ')
            return

        reference = cmds.ls(reference, l=1)[0]
        return reference

    @classmethod
    def silent_mode_connect(cls, ref, item):
        cmds.connectAttr(ref + '.outMesh', item + '.inMesh', f=1)

    @classmethod
    def silent_mode_parenting(cls, item, child):
        parent = cmds.listRelatives(item, p=1, f=1)
        cmds.delete(item)
        if parent:
            cmds.parent(child, parent)
        else:
            cmds.rename(child, item)

    @classmethod
    def silent_mode_layer(cls, item):
        display_layer = cmds.listConnections(item, type="displayLayer")
        if display_layer:
            return display_layer[0]
        else:
            return None

    @classmethod
    def silent_mode_duplicate(cls, ref):
        cmds.select(ref)
        duplicate_ref = cmds.duplicate(rr=1, un=1)
        duplicate_ref = cmds.ls(sl=1, l=1)
        return duplicate_ref

    @classmethod
    def silent_mode_unskinned(cls, duplicate):
        try:
            cmds.skinCluster(duplicate, e=1, ub=1)
            cmds.delete('nodes_01')
        except:
            pass

    @classmethod
    def silent_mode_clear(cls):
        try:
            cmds.delete('nodes_01')
        except:
            pass

    @classmethod
    def silent_mode_replace(cls, ref, item, layer):
        duplicate_ref = cls.silent_mode_duplicate(ref)
        cls.silent_mode_unskinned(duplicate_ref)
        # cls.silent_mode_clear()
        if layer:
            cmds.editDisplayLayerMembers(layer, duplicate_ref)

        cls.silent_mode_parenting(item, duplicate_ref)

    @classmethod
    def silent_mode(cls, source_folder_name):
        selection = cls.silent_mode_selection()

        current_file_path = cmds.file(q=True, exn=True)
        if not os.path.isfile(current_file_path):
            cmds.confirmDialog(title='Warning', message='Save file before', button=['   OK   '],
                               defaultButton='   OK   ')
            cmds.error()

        if source_folder_name in current_file_path:
            cmds.confirmDialog(title='Warning', message='The open file is in the ' + source_folder_name + ' directory ',
                               button=['   OK   '], defaultButton='   OK   ')
            cmds.error()

        upper_folders = cls.two_up_folder(current_file_path)
        srs_folder = cls.source_folder(upper_folders, source_folder_name)
        current_file_name = posixpath.split(current_file_path)[1]
        srs_file = cls.source_file(srs_folder, current_file_name)
        cls.silent_mode_load(srs_file)

        for item in selection:
            ref = cls.silent_mode_references(item)
            layer = cls.silent_mode_layer(item)
            if ref:
                cls.silent_mode_replace(ref, item, layer)

        cls.delete_reference()


class ImportReplace_Wnd(QDialog):
    MINIMUM_WIDTH = 360
    MINIMUM_HEIGHT = 500
    BROWSER_DIALOG_STYLE = 1

    def __init__(self, parent=main_window_pointer()):
        QDialog.__init__(self, parent)

        self.setWindowTitle("Import/Replace")
        self.setModal(False)
        self.setMinimumWidth(self.MINIMUM_WIDTH)
        self.setMinimumHeight(self.MINIMUM_HEIGHT)
        self.setWindowFlags(Qt.Window)
        self.setObjectName("ImportReplaceObject")

        self.setup_browser_line()
        self.setup_files_list()
        self.buttons()
        self.layouts()

        # save position window
        self.settings = QSettings("ImportReplaceObject")
        if not self.settings.value("geometry") == None:
            self.restoreGeometry(self.settings.value("geometry"))

    def setup_browser_line(self):
        self.directory_lbl = QLabel("Dir")
        self.line_edit = QLineEdit()
        self.select_dir_btn = self.init_button(command=self.dir_browser, icon=":fileOpen.png", w=42, h=20)
        self.select_dir_btn.setToolTip("Select directory with .mb files")

    def outliner_panel(self):
        cmds.setParent('outLayout')
        cmds.paneLayout()
        panel = cmds.outlinerPanel()
        outliner = cmds.outlinerPanel(panel, query=True, outlinerEditor=True)
        cmds.outlinerEditor(outliner, edit=True, mainListConnection='worldList', selectionConnection='modelList',
                            showShapes=False, showReferenceNodes=False, showReferenceMembers=False,
                            showAttributes=False, showConnected=False, showAnimCurvesOnly=False, autoExpand=False,
                            showDagOnly=True, ignoreDagHierarchy=False, expandConnections=False, showNamespace=True,
                            showCompounds=True, showNumericAttrsOnly=False, highlightActive=True,
                            autoSelectNewObjects=False, doNotSelectNewObjects=False, transmitFilters=False,
                            showSetMembers=False, setFilter='defaultSetFilter', ignoreHiddenAttribute=False,
                            ignoreOutlinerColor=False)

        return panel, outliner

    def panel_point(self, panel):
        ptr = omu.MQtUtil.findControl(panel)
        panel_layout = shiboken2.wrapInstance(int(ptr), QWidget)
        return panel_layout

    def setup_files_list(self):
        path, file = Utils.maya_scene_name()
        self.line_edit.setText(path)

        # Model
        self.setup_model(path, file)

        # ListView
        self.list_view = QListView(self)
        self.list_view.setModel(self.model)
        self.list_view.setRootIndex(self.model.setRootPath(path))
        self.list_view.setMaximumHeight(100)
        self.selModel = self.list_view.selectionModel()
        self.list_view.clicked.connect(self.onclick)

        # Splitter
        self.splitter = QSplitter(Qt.Horizontal)
        self.splitter.setObjectName('outLayout')

        # Left outliner
        panel_left, outliner_left = self.outliner_panel()
        filters = cmds.itemFilter(bn=('*:*'))
        cmds.outlinerEditor(outliner_left, e=1, filter=filters)
        panel_layout_left = self.panel_point(panel_left)

        # Right outliner
        panel_right, outliner_right = self.outliner_panel()
        mesh = cmds.itemFilter(byType='mesh')
        dif_filter = cmds.itemFilter(difference=(mesh, filters))
        cmds.outlinerEditor(outliner_right, e=1, filter=dif_filter)
        panel_layout_right = self.panel_point(panel_right)

        self.splitter.addWidget(panel_layout_left)
        self.splitter.addWidget(panel_layout_right)

    def setup_model(self, path, file):
        self.model = QFileSystemModel()
        self.model.setRootPath(path)
        self.model.setFilter(QDir.Files)
        self.model.setNameFilters(self.get_filter_file(path, file))
        self.model.setNameFilterDisables(False)

    def get_filter_file(self, path, file):
        filter_file = [entry for entry in os.listdir(path) if entry != file and entry.endswith('.mb')]
        return filter_file if filter_file else ['']

    def buttons(self):
        self.import_btn = self.init_button(title='Import', command=lambda: Utils.import_action(), h=20)
        self.import_btn.setToolTip("Import selected")
        self.replace_btn = self.init_button(title='Replace', command=lambda: Utils.replace_action(), h=20)
        self.replace_btn.setToolTip("Replace selected from left panel to right panel")

    def init_button(self, parent=None, title="", command=None, icon=None, w=None, h=None):
        self._button = QPushButton(title)
        if command:
            self._button.clicked.connect(command)
        if icon:
            self._button.setIcon(QIcon(icon))
        if w:
            self._button.setMinimumWidth(w)
            self._button.setMaximumWidth(w)
        if h:
            self._button.setMinimumHeight(h)
            self._button.setMaximumHeight(h)
        return self._button

    def dir_browser(self):  # open browser to set export path
        browser_path = None
        try:
            browser_path = cmds.fileDialog2(fm=3, dialogStyle=1)[0]
        except:
            pass

        if browser_path:
            self.line_edit.setText(browser_path)
            self.list_view.setRootIndex(self.model.setRootPath(browser_path))
            self.model.setNameFilters(["*.mb"])
            self.model.setNameFilterDisables(False)

        else:
            cmds.confirmDialog(title='Warning', message='Select dir with *.mb files', button=['   OK   '],
                               defaultButton='   OK   ')

    def layouts(self):
        main_layout = QVBoxLayout(self)

        browser_layout = QHBoxLayout(self)
        browser_layout.addWidget(self.directory_lbl)
        browser_layout.addWidget(self.line_edit)
        browser_layout.addWidget(self.select_dir_btn)

        treeLayout = QVBoxLayout(self)
        treeLayout.addWidget(self.list_view)
        treeLayout.addWidget(self.splitter)

        buttonLayout = QHBoxLayout(self)
        buttonLayout.addWidget(self.import_btn)
        buttonLayout.addWidget(self.replace_btn)

        main_layout.addLayout(browser_layout)
        main_layout.addLayout(treeLayout)
        main_layout.addLayout(buttonLayout)

    def onclick(self, index):
        item = self.selModel.selection().indexes()[0]
        path = self.line_edit.text()
        path = path + '/' + item.data()
        Utils.reference_load_unload(path)

    def closeEvent(self, event):
        # restore position window
        self.settings.setValue("geometry", self.saveGeometry())
        Utils.delete_reference()

    def script_job(self):
        cmds.script_job(uid=["ImportReplaceObject", lambda: Utils.delete_reference()])


def main():
    if cmds.window("ImportReplaceObject", q=True, exists=True):
        cmds.deleteUI("ImportReplaceObject")
    try:
        cmds.deleteUI('MayaWindow|ImportReplaceObject')
    except:
        pass

    try:
        dialog = ImportReplace_Wnd()
        dialog.close()
    except:
        pass

    dialog = ImportReplace_Wnd()
    dialog.show()


if __name__ == '__main__':
    main()
